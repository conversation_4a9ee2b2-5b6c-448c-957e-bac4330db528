# 账号管理与登录系统

一个基于PyQt5和DrissionPage的账号管理与自动登录系统，支持多账号存储、管理和自动登录功能。

## 功能特性

### 🔐 账号管理
- **JSON数据存储**: 使用JSON文件安全存储多个账号信息
- **账号CRUD操作**: 支持添加、查看、更新、删除账号
- **账号选择**: 便捷的下拉框选择已保存的账号
- **数据持久化**: 自动保存和加载账号数据

### 🖥️ 用户界面
- **现代化界面**: 基于PyQt5的美观用户界面
- **分区布局**: 左侧账号管理，右侧操作日志
- **实时反馈**: 操作日志实时显示系统状态
- **表单验证**: 完善的输入验证和错误提示

### 🌐 自动登录
- **DrissionPage集成**: 使用DrissionPage进行浏览器自动化
- **多线程登录**: 登录过程不阻塞界面操作
- **登录状态反馈**: 实时显示登录进度和结果
- **网站适配**: 支持自定义网站登录逻辑

## 系统架构

```
账号管理与登录系统/
├── account_manager.py    # AccountManager类 - 账号数据管理
├── login_window.py       # LoginWindow类 - PyQt5主界面
├── accounts.json         # JSON数据文件 - 存储账号信息
├── main.py              # 主程序入口
├── requirements.txt     # 依赖包列表
└── README.md           # 使用说明
```

### 核心类说明

#### AccountManager类
- **职责**: 处理账号数据的加载、保存和管理
- **功能**: JSON文件操作、账号CRUD、数据验证
- **文件**: `account_manager.py`

#### LoginWindow类
- **职责**: PyQt5主界面和用户交互
- **功能**: 界面布局、事件处理、DrissionPage集成
- **文件**: `login_window.py`

## 安装和使用

### 1. 环境要求
- Python 3.7+
- Windows/macOS/Linux

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

或者手动安装：
```bash
pip install PyQt5 DrissionPage requests selenium
```

### 3. 运行程序
```bash
python main.py
```

## 界面功能说明

### 账号管理区域（左侧）

#### 账号选择
- **下拉框**: 选择已保存的账号
- **自动填充**: 选择账号后自动填充表单信息

#### 账号信息表单
- **账号名称**: 账号的唯一标识符
- **用户名**: 登录用户名
- **密码**: 登录密码（加密显示）
- **描述**: 账号描述信息
- **网站地址**: 登录网站URL

#### 操作按钮
- **保存账号**: 保存新账号或更新现有账号
- **删除账号**: 删除选中的账号
- **清空表单**: 清空所有输入框
- **登录**: 使用DrissionPage自动登录

### 日志区域（右侧）
- **操作日志**: 实时显示系统操作和状态
- **时间戳**: 每条日志都有精确的时间记录
- **清空日志**: 清空日志内容

## 使用流程

### 1. 添加账号
1. 在"账号名称"中输入唯一的账号标识
2. 填写用户名、密码等信息
3. 点击"保存账号"按钮
4. 系统会自动保存到JSON文件

### 2. 管理账号
1. 从下拉框选择要管理的账号
2. 修改账号信息后点击"保存账号"更新
3. 点击"删除账号"可删除选中账号

### 3. 自动登录
1. 选择要登录的账号
2. 确认网站地址正确
3. 点击"登录"按钮
4. 系统会启动浏览器并自动登录

## 数据存储格式

账号信息存储在`accounts.json`文件中：

```json
{
  "accounts": {
    "账号名称": {
      "username": "用户名",
      "password": "密码",
      "description": "描述",
      "website": "网站地址",
      "created_time": "创建时间",
      "last_used": "最后使用时间"
    }
  },
  "last_updated": "最后更新时间",
  "version": "版本号"
}
```

## 自定义登录逻辑

在`login_window.py`的`LoginThread.run()`方法中，您可以根据具体网站自定义登录逻辑：



## 安全注意事项

1. **密码存储**: 当前版本密码以明文存储在JSON文件中，建议在生产环境中使用加密存储
2. **文件权限**: 确保`accounts.json`文件的访问权限设置合理
3. **备份数据**: 定期备份账号数据文件

## 故障排除

### 常见问题

1. **依赖包安装失败**
   ```bash
   # 使用国内镜像源
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ PyQt5 DrissionPage
   ```

2. **DrissionPage浏览器启动失败**
   - 确保系统已安装Chrome浏览器
   - 检查Chrome版本是否与ChromeDriver兼容

3. **界面显示异常**
   - 检查PyQt5版本是否正确安装
   - 尝试重新安装PyQt5

### 日志调试
系统会在界面右侧显示详细的操作日志，遇到问题时请查看日志信息进行调试。

## 版本信息
- **当前版本**: 1.0.0
- **Python版本**: 3.7+
- **PyQt5版本**: 5.15.0+
- **DrissionPage版本**: 4.0.0+

## 许可证
本项目采用MIT许可证，详情请参见LICENSE文件。
