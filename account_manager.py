#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理器
负责处理账号数据的加载、保存和管理
"""

import json
import os
from typing import Dict, List, Optional
from datetime import datetime


class AccountManager:
    """账号管理器类，处理账号数据和API密钥的加载和保存"""

    def __init__(self, json_file: str = "accounts.json", verbose: bool = False):
        """
        初始化账号管理器

        Args:
            json_file: JSON文件路径
            verbose: 是否输出详细日志，默认为 False
        """
        self.json_file = json_file
        self.accounts = {}
        self.api_keys = []
        self.current_api_index = 0
        self.verbose = verbose
        self.load_accounts()

    def _log(self, message: str):
        """内部日志方法，只在verbose模式下输出"""
        if self.verbose:
            print(message)

    def load_accounts(self) -> bool:
        """
        从JSON文件加载账号信息和API密钥

        Returns:
            bool: 加载是否成功
        """
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.accounts = data.get('accounts', {})
                    self.api_keys = data.get('api_keys', [])
                    self.current_api_index = data.get('current_api_index', 0)

                    # 确保current_api_index在有效范围内
                    if self.api_keys and self.current_api_index >= len(self.api_keys):
                        self.current_api_index = 0

                self._log(f"✅ 成功加载 {len(self.accounts)} 个账号")
                self._log(f"✅ 成功加载 {len(self.api_keys)} 个API密钥")
                return True
            else:
                # 如果文件不存在，创建空的数据结构
                self.accounts = {}
                self.api_keys = []
                self.current_api_index = 0
                self.save_accounts()
                self._log("📝 创建新的账号文件")
                return True
        except Exception as e:
            self._log(f"❌ 加载账号文件失败: {e}")
            self.accounts = {}
            self.api_keys = []
            self.current_api_index = 0
            return False
    
    def save_accounts(self) -> bool:
        """
        保存账号信息到JSON文件
        
        Returns:
            bool: 保存是否成功
        """
        try:
            data = {
                'accounts': self.accounts,
                'api_keys': self.api_keys,
                'current_api_index': self.current_api_index,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }

            with open(self.json_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            self._log(f"✅ 成功保存 {len(self.accounts)} 个账号和 {len(self.api_keys)} 个API密钥到 {self.json_file}")
            return True
        except Exception as e:
            self._log(f"❌ 保存账号文件失败: {e}")
            return False

    # API密钥管理方法
    def add_api_key(self, app_id: str, secret_code: str, description: str = "") -> bool:
        """
        添加API密钥

        Args:
            app_id: API应用ID
            secret_code: API密钥
            description: 描述信息

        Returns:
            bool: 添加是否成功
        """
        try:
            # 检查是否已存在相同的app_id
            for key in self.api_keys:
                if key.get('app_id') == app_id:
                    self._log(f"⚠️ API密钥 {app_id} 已存在")
                    return False

            api_key = {
                'app_id': app_id,
                'secret_code': secret_code,
                'description': description,
                'status': 'active',  # active, exhausted, error
                'created_time': datetime.now().isoformat(),
                'last_used': None,
                'usage_count': 0
            }

            self.api_keys.append(api_key)
            self.save_accounts()
            self._log(f"✅ 成功添加API密钥: {app_id}")
            return True

        except Exception as e:
            self._log(f"❌ 添加API密钥失败: {e}")
            return False

    def get_current_api_key(self) -> dict:
        """
        获取当前可用的API密钥

        Returns:
            dict: API密钥信息，如果没有可用密钥返回None
        """
        if not self.api_keys:
            return None

        # 从当前索引开始查找可用的API密钥
        for i in range(len(self.api_keys)):
            index = (self.current_api_index + i) % len(self.api_keys)
            api_key = self.api_keys[index]

            if api_key.get('status') == 'active':
                self.current_api_index = index
                return api_key

        # 如果没有找到active状态的密钥，返回第一个
        return self.api_keys[0] if self.api_keys else None

    def mark_api_key_exhausted(self, app_id: str) -> bool:
        """
        标记API密钥为余额不足

        Args:
            app_id: API应用ID

        Returns:
            bool: 标记是否成功
        """
        try:
            for api_key in self.api_keys:
                if api_key.get('app_id') == app_id:
                    api_key['status'] = 'exhausted'
                    api_key['exhausted_time'] = datetime.now().isoformat()
                    self._log(f"⚠️ API密钥 {app_id} 已标记为余额不足")

                    # 自动切换到下一个可用密钥
                    self.switch_to_next_api_key()
                    self.save_accounts()
                    return True

            self._log(f"⚠️ 未找到API密钥: {app_id}")
            return False

        except Exception as e:
            self._log(f"❌ 标记API密钥失败: {e}")
            return False

    def switch_to_next_api_key(self) -> dict:
        """
        切换到下一个可用的API密钥

        Returns:
            dict: 下一个可用的API密钥，如果没有返回None
        """
        if not self.api_keys:
            return None

        # 查找下一个active状态的密钥
        for i in range(1, len(self.api_keys) + 1):
            next_index = (self.current_api_index + i) % len(self.api_keys)
            api_key = self.api_keys[next_index]

            if api_key.get('status') == 'active':
                self.current_api_index = next_index
                self._log(f"🔄 已切换到API密钥: {api_key.get('app_id')}")
                return api_key

        self._log("⚠️ 没有可用的API密钥")
        return None

    def update_api_key_usage(self, app_id: str) -> bool:
        """
        更新API密钥使用记录

        Args:
            app_id: API应用ID

        Returns:
            bool: 更新是否成功
        """
        try:
            for api_key in self.api_keys:
                if api_key.get('app_id') == app_id:
                    api_key['last_used'] = datetime.now().isoformat()
                    api_key['usage_count'] = api_key.get('usage_count', 0) + 1
                    return True
            return False
        except Exception as e:
            self._log(f"❌ 更新API密钥使用记录失败: {e}")
            return False

    def get_all_api_keys(self) -> list:
        """
        获取所有API密钥信息

        Returns:
            list: API密钥列表
        """
        return self.api_keys.copy()

    def remove_api_key(self, app_id: str) -> bool:
        """
        删除API密钥

        Args:
            app_id: API应用ID

        Returns:
            bool: 删除是否成功
        """
        try:
            for i, api_key in enumerate(self.api_keys):
                if api_key.get('app_id') == app_id:
                    self.api_keys.pop(i)

                    # 如果删除的是当前使用的密钥，重置索引
                    if self.current_api_index >= len(self.api_keys):
                        self.current_api_index = 0

                    self.save_accounts()
                    self._log(f"✅ 成功删除API密钥: {app_id}")
                    return True

            self._log(f"⚠️ 未找到API密钥: {app_id}")
            return False

        except Exception as e:
            self._log(f"❌ 删除API密钥失败: {e}")
            return False

    def add_account(self, account_name: str, username: str, password: str,
                   description: str = "", website: str = "") -> bool:
        """
        添加新账号（保持向后兼容）

        Args:
            account_name: 账号名称（唯一标识）
            username: 用户名
            password: 密码
            description: 账号描述
            website: 网站地址

        Returns:
            bool: 添加是否成功
        """
        if not account_name or not username or not password:
            self._log("❌ 账号名称、用户名和密码不能为空")
            return False

        self.accounts[account_name] = {
            'username': username,
            'password': password,
            'description': description,
            'website': website,
            'created_time': datetime.now().isoformat(),
            'last_used': None
        }

        if self.save_accounts():
            self._log(f"✅ 成功添加账号: {account_name}")
            return True
        return False

    def add_account_with_dates(self, account_name: str, password: str,
                              start_date: str = "", end_date: str = "") -> bool:
        """
        添加新账号（带日期）

        Args:
            account_name: 账号名称（唯一标识）
            password: 密码
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            bool: 添加是否成功
        """
        if not account_name or not password:
            self._log("❌ 账号名称和密码不能为空")
            return False

        self.accounts[account_name] = {
            'password': password,
            'start_date': start_date,
            'end_date': end_date,
            'created_time': datetime.now().isoformat(),
            'last_used': None
        }

        if self.save_accounts():
            self._log(f"✅ 成功添加账号: {account_name}")
            return True
        return False
    
    def update_account(self, account_name: str, username: str = None,
                      password: str = None, description: str = None,
                      website: str = None) -> bool:
        """
        更新账号信息（保持向后兼容）

        Args:
            account_name: 账号名称
            username: 新用户名（可选）
            password: 新密码（可选）
            description: 新描述（可选）
            website: 新网站地址（可选）

        Returns:
            bool: 更新是否成功
        """
        if account_name not in self.accounts:
            self._log(f"❌ 账号 {account_name} 不存在")
            return False

        account = self.accounts[account_name]

        if username is not None:
            account['username'] = username
        if password is not None:
            account['password'] = password
        if description is not None:
            account['description'] = description
        if website is not None:
            account['website'] = website

        account['updated_time'] = datetime.now().isoformat()

        if self.save_accounts():
            self._log(f"✅ 成功更新账号: {account_name}")
            return True
        return False

    def update_account_with_dates(self, account_name: str, password: str = None,
                                 start_date: str = None, end_date: str = None) -> bool:
        """
        更新账号信息（带日期）

        Args:
            account_name: 账号名称
            password: 新密码（可选）
            start_date: 新开始日期（可选）
            end_date: 新结束日期（可选）

        Returns:
            bool: 更新是否成功
        """
        if account_name not in self.accounts:
            self._log(f"❌ 账号 {account_name} 不存在")
            return False

        account = self.accounts[account_name]

        if password is not None:
            account['password'] = password
        if start_date is not None:
            account['start_date'] = start_date
        if end_date is not None:
            account['end_date'] = end_date

        account['updated_time'] = datetime.now().isoformat()

        if self.save_accounts():
            self._log(f"✅ 成功更新账号: {account_name}")
            return True
        return False
    
    def delete_account(self, account_name: str) -> bool:
        """
        删除账号
        
        Args:
            account_name: 账号名称
            
        Returns:
            bool: 删除是否成功
        """
        if account_name not in self.accounts:
            self._log(f"❌ 账号 {account_name} 不存在")
            return False

        del self.accounts[account_name]

        if self.save_accounts():
            self._log(f"✅ 成功删除账号: {account_name}")
            return True
        return False
    
    def get_account(self, account_name: str) -> Optional[Dict]:
        """
        获取账号信息
        
        Args:
            account_name: 账号名称
            
        Returns:
            Dict: 账号信息，如果不存在返回None
        """
        return self.accounts.get(account_name)
    
    def get_all_accounts(self) -> Dict:
        """
        获取所有账号信息
        
        Returns:
            Dict: 所有账号信息
        """
        return self.accounts.copy()
    
    def get_account_names(self) -> List[str]:
        """
        获取所有账号名称列表
        
        Returns:
            List[str]: 账号名称列表
        """
        return list(self.accounts.keys())
    
    def account_exists(self, account_name: str) -> bool:
        """
        检查账号是否存在
        
        Args:
            account_name: 账号名称
            
        Returns:
            bool: 账号是否存在
        """
        return account_name in self.accounts
    
    def update_last_used(self, account_name: str) -> bool:
        """
        更新账号最后使用时间
        
        Args:
            account_name: 账号名称
            
        Returns:
            bool: 更新是否成功
        """
        if account_name not in self.accounts:
            return False
        
        self.accounts[account_name]['last_used'] = datetime.now().isoformat()
        return self.save_accounts()
    
    def get_account_count(self) -> int:
        """
        获取账号总数
        
        Returns:
            int: 账号总数
        """
        return len(self.accounts)


# 使用示例
if __name__ == "__main__":
    # 创建账号管理器
    manager = AccountManager()
    
    # 添加测试账号
    manager.add_account("测试账号1", "user1", "pass1", "这是测试账号1", "https://example.com")
    manager.add_account("测试账号2", "user2", "pass2", "这是测试账号2", "https://test.com")
    
    # 显示所有账号
    print("\n所有账号:")
    for name in manager.get_account_names():
        account = manager.get_account(name)
        print(f"- {name}: {account['username']} ({account['description']})")

    # 更新账号
    manager.update_account("测试账号1", description="更新后的描述")

    # 删除账号
    manager.delete_account("测试账号2")

    print(f"\n最终账号数量: {manager.get_account_count()}")
