# 报关单下载系统 v1.0.0

## 使用说明

### 首次使用
1. 双击运行 `报关单下载系统.exe`
2. 点击"API密钥管理"按钮添加PDF识别API密钥
3. 添加或选择登录账号
4. 设置查询日期范围
5. 设置分块天数（可选，默认7天）
6. 点击"登录"开始自动化流程

### 功能特性
- ✅ 自动登录报关系统
- ✅ 自动下载PDF报关单
- ✅ 智能识别PDF内容
- ✅ 自动重命名文件
- ✅ 生成Excel分析报告
- ✅ 多API密钥自动切换
- ✅ 账号目录自动管理
- ✅ PDF文件名列表导出
- ✅ 日期范围分块查询（避免单次查询数据过多）

### 文件说明
- `报关单下载系统.exe` - 主程序
- `accounts.json` - 账号和API密钥配置文件
- `README.txt` - 本说明文件

### 输出目录
程序会在桌面创建以下目录结构：
```
桌面/
└── 账号/
    └── 账号-开始日期-结束日期/
        ├── 报关单数据_时间戳.xlsx     (原始查询数据)
        ├── PDF识别结果.xlsx          (PDF识别分析报告)
        ├── 账号-PDF文件名列表.xlsx   (PDF文件名清单)
        └── pdf/
            ├── 1_合同号_商品名_海关编号_总价.pdf
            ├── 2_合同号_商品名_海关编号_总价.pdf
            └── ...
```

### 分块查询功能
当查询日期范围较大时，系统会自动将日期范围分成多个小块进行查询：
- **分块天数设置**：可在界面上设置每个时间块的天数（1-30天，默认7天）
- **自动分块**：系统会根据设置自动将大日期范围分割成多个小块
- **分块查询**：对每个时间块分别进行查询，避免单次查询数据过多导致超时
- **结果合并**：自动合并所有时间块的查询结果
- **进度显示**：实时显示当前处理的时间块进度

示例：查询2025-01-01到2025-01-31（31天），设置分块天数为7天
- 时间块1: 2025-01-01 到 2025-01-07
- 时间块2: 2025-01-08 到 2025-01-14
- 时间块3: 2025-01-15 到 2025-01-21
- 时间块4: 2025-01-22 到 2025-01-28
- 时间块5: 2025-01-29 到 2025-01-31

### 注意事项
1. 首次运行需要联网下载浏览器驱动
2. 确保网络连接稳定
3. API密钥余额不足时会自动切换
4. 建议添加多个备用API密钥
5. 如果杀毒软件报警，请添加信任
6. 日期范围较大时建议使用较小的分块天数（3-7天）

### 故障排除
如果程序无法启动：
1. 确保Windows系统已安装最新的Visual C++运行库
2. 尝试以管理员身份运行
3. 检查杀毒软件是否阻止了程序运行
4. 确保有足够的磁盘空间

### 技术规格
- 打包工具: PyInstaller 6.3.0
- 打包模式: 单文件exe (--onefile)
- 界面模式: 窗口模式 (--windowed)
- Python版本: 3.11.9
- 平台: Windows 10/11

### 更新日志
v1.0.0 (2025-07-31)
- ✅ 完整的自动化报关单下载流程
- ✅ PDF识别和智能重命名
- ✅ 多API密钥管理和自动切换
- ✅ 桌面目录自动管理
- ✅ Excel报告生成
- ✅ PDF文件名列表导出
- ✅ 优化的日志系统
- ✅ 去掉登录成功弹窗
- ✅ 专业级用户界面

---
生成时间: 2025-07-31
版本: 1.0.0
