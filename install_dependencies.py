#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
依赖包安装脚本
自动安装报关单下载工具所需的所有依赖包
"""

import subprocess
import sys
import os


def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("❌ Python版本过低，建议使用Python 3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True


def install_package(package_name):
    """安装单个包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", package_name
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print(f"✅ {package_name} 安装成功")
            return True
        else:
            print(f"❌ {package_name} 安装失败:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print(f"❌ {package_name} 安装超时")
        return False
    except Exception as e:
        print(f"❌ {package_name} 安装出错: {e}")
        return False


def install_from_requirements():
    """从requirements.txt安装依赖"""
    requirements_file = "requirements.txt"
    
    if not os.path.exists(requirements_file):
        print(f"❌ 找不到 {requirements_file} 文件")
        return False
    
    try:
        print(f"📋 从 {requirements_file} 安装依赖...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", requirements_file
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ 所有依赖安装成功")
            return True
        else:
            print("❌ 依赖安装失败:")
            print(result.stderr)
            return False
    except subprocess.TimeoutExpired:
        print("❌ 依赖安装超时")
        return False
    except Exception as e:
        print(f"❌ 依赖安装出错: {e}")
        return False


def check_installed_packages():
    """检查已安装的包"""
    print("\n🔍 检查已安装的包...")
    
    required_packages = [
        "PyQt5",
        "DrissionPage", 
        "requests",
        "pandas",
        "openpyxl"
    ]
    
    installed_packages = []
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.lower().replace("-", "_"))
            installed_packages.append(package)
            print(f"✅ {package} - 已安装")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package} - 未安装")
    
    return installed_packages, missing_packages


def upgrade_pip():
    """升级pip"""
    try:
        print("🔧 升级pip...")
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ pip升级成功")
            return True
        else:
            print("⚠️ pip升级失败，但可以继续安装")
            return True  # pip升级失败不影响后续安装
    except Exception as e:
        print(f"⚠️ pip升级出错: {e}，但可以继续安装")
        return True


def main():
    """主函数"""
    print("🚀 报关单下载工具 - 依赖包安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        print("\n❌ 安装失败：Python版本不符合要求")
        return False
    
    # 升级pip
    upgrade_pip()
    
    # 检查当前已安装的包
    installed, missing = check_installed_packages()
    
    if not missing:
        print("\n🎉 所有依赖包都已安装！")
        return True
    
    print(f"\n📦 需要安装 {len(missing)} 个包: {', '.join(missing)}")
    
    # 尝试从requirements.txt安装
    if install_from_requirements():
        print("\n🎉 依赖安装完成！")
        
        # 再次检查
        print("\n🔍 验证安装结果...")
        installed, missing = check_installed_packages()
        
        if not missing:
            print("\n✅ 所有依赖包安装成功！")
            print("\n📋 已安装的包:")
            for package in installed:
                print(f"  • {package}")
            
            print("\n🎯 现在可以运行报关单下载工具了！")
            print("运行命令: python login_window.py")
            return True
        else:
            print(f"\n⚠️ 仍有 {len(missing)} 个包未安装: {', '.join(missing)}")
            return False
    else:
        print("\n❌ 依赖安装失败")
        print("\n🔧 故障排除建议:")
        print("1. 检查网络连接")
        print("2. 尝试使用国内镜像源:")
        print("   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/")
        print("3. 手动安装单个包:")
        for package in missing:
            print(f"   pip install {package}")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n按回车键退出...")
        else:
            input("\n安装失败，按回车键退出...")
    except KeyboardInterrupt:
        print("\n\n用户取消安装")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        input("按回车键退出...")
