#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
登录窗口界面
PyQt5主界面，包含账号管理和登录功能
"""

import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QLabel, QLineEdit, QPushButton, QComboBox,
                             QTextEdit, QMessageBox, QGroupBox, QFormLayout,
                             QSplitter, QFrame, QDateEdit, QFileDialog)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QDate
from PyQt5.QtGui import QFont, QIcon
from account_manager import AccountManager
import traceback
from DrissionPage import ChromiumPage, ChromiumOptions, SessionPage
import time
import requests
import json
import urllib.parse
from datetime import datetime, timedelta
from enum import Enum
import tempfile
import uuid
import pandas as pd
import os
import tempfile
import uuid
from openpyxl import load_workbook
from openpyxl.styles import Font, Alignment
from pathlib import Path
import re
from openpyxl.utils import get_column_letter


class TableFlag(Enum):
    """表格标志枚举"""
    NO = "0"   # 选择否
    YES = "1"  # 选择是

    @classmethod
    def get_description(cls, value):
        """获取枚举值的描述"""
        descriptions = {
            cls.NO.value: "否",
            cls.YES.value: "是"
        }
        return descriptions.get(value, "未知")


class IEFlag(Enum):
    """进出口标志枚举"""
    EXPORT = "E"  # 出口
    IMPORT = "I"  # 进口

    @classmethod
    def get_description(cls, value):
        """获取枚举值的描述"""
        descriptions = {
            cls.EXPORT.value: "出口",
            cls.IMPORT.value: "进口"
        }
        return descriptions.get(value, "未知")


class EtpsCategory(Enum):
    """企业类型枚举"""
    COMPANY = "C"      # 企业
    INDIVIDUAL = "I"   # 个人

    @classmethod
    def get_description(cls, value):
        """获取枚举值的描述"""
        descriptions = {
            cls.COMPANY.value: "企业",
            cls.INDIVIDUAL.value: "个人"
        }
        return descriptions.get(value, "未知")


# 字段映射配置
FIELD_MAPPING = {
    '统一编号': 'cusCiqNo',
    '海关编号': 'entryId',
    '境内收发货人': 'consignorCname',
    '境内收发货人18位社会信用代码': 'cnsnTradeScc',
    '提运单号': 'billNo',
    '进出口日期': 'iEDate',
    '监管方式': 'supvModeCddeName',
    '合同协议号': 'contrNo',
    '商品项数': 'goodsNum',
    '运输工具名称': 'trafName',
    '航次号': 'cusVoyageNo',
    '报关状态': 'cusDecStatusName',
    '是否查验': 'chktstFlag',
    '申报单位名称': 'agentName',
    '进出口标志': 'cusIEFlagName',
    '申报地海关': 'customMasterName',
    '入境/离境口岸': 'despPortCodeName',
    '集装箱数量': 'contaCount',
}

# PDF识别相关配置（将从账号管理器动态获取）
PDF_RECOGNITION_API_URL = 'https://api.textin.com/ai/service/v1/customs_declaration?multipage=1'

# 商品单位映射字典
UNIT_DICT = {
    "回球器": "个", "火盆": "个", "木板铁床": "件", "三层床头柜": "件", "椅子": "件",
    "金属探测器": "台", "铁架沙发": "个", "铁架茶几": "件", "铁架椅": "个", "篮板": "件",
    "柜子": "件", "桌子": "件", "三抽柜": "件", "儿童电动车": "辆", "多功能球台": "件",
    "桌球台": "件", "爬架": "件", "手推车": "件", "玩具屋": "件", "玩具厨房": "件",
    "投篮机": "套", "学步车": "辆", "吸尘器": "件", "燃气炉": "件", "桌面小火炉": "件",
    "球池": "件", "球池套装": "件", "投篮机套装": "件", "沙发": "件", "发电机": "件",
    "梳妆台": "件", "油桶炉": "件", "餐桌": "件", "餐椅": "个", "四抽柜": "件",
    "篮球架": "件", "气炉": "件", "蹦床": "件", "储物箱": "件", "电动童车": "辆",
    "木柜": "件", "装饰灯": "件", "床架": "件", "烧烤炉": "个", "吊椅": "个",
    "喂鸟器": "件", "椅子套装": "件"
}


def apply_excel_style(output_file: str) -> None:
    """应用Excel样式"""
    wb = load_workbook(output_file)

    for sheet_name in wb.sheetnames:
        ws = wb[sheet_name]

        # 设置标题栏样式（首行）
        header_font = Font(name='等线', bold=True, size=12)
        header_alignment = Alignment(wrap_text=True, vertical='center')

        if ws.max_row >= 1:
            for cell in ws[1]:
                cell.font = header_font
                cell.alignment = header_alignment

        # 设置内容栏样式
        content_font = Font(name='等线', size=11)
        content_alignment = Alignment(wrap_text=False, vertical='center')

        for row in ws.iter_rows(min_row=2):
            for cell in row:
                cell.font = content_font
                cell.alignment = content_alignment

        # 自动调整列宽（已修复部分）
        for col_idx, column in enumerate(ws.columns, 1):
            max_length = 0
            column_letter = get_column_letter(col_idx)  # 使用新方法获取列字母
            for cell in column:
                try:
                    value_length = len(str(cell.value))
                    max_length = max(max_length, value_length)
                except:
                    pass
            adjusted_width = (max_length + 2) * 1.2
            ws.column_dimensions[column_letter].width = adjusted_width

    wb.save(output_file)


def get_default_save_path():
    """
    获取默认保存路径（桌面），考虑打包情况

    Returns:
        str: 默认保存路径
    """
    try:
        # 方法1：使用os.path.expanduser获取用户主目录下的桌面
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        if os.path.exists(desktop_path):
            return desktop_path

        # 方法2：尝试中文桌面路径
        desktop_path_cn = os.path.join(os.path.expanduser("~"), "桌面")
        if os.path.exists(desktop_path_cn):
            return desktop_path_cn

        # 方法3：Windows系统特定路径
        if os.name == 'nt':  # Windows系统
            try:
                import winreg
                # 从注册表获取桌面路径
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER,
                                   r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders")
                desktop_path, _ = winreg.QueryValueEx(key, "Desktop")
                winreg.CloseKey(key)
                if os.path.exists(desktop_path):
                    return desktop_path
            except:
                pass

            # Windows环境变量方式
            userprofile = os.environ.get('USERPROFILE')
            if userprofile:
                desktop_path = os.path.join(userprofile, "Desktop")
                if os.path.exists(desktop_path):
                    return desktop_path
                desktop_path_cn = os.path.join(userprofile, "桌面")
                if os.path.exists(desktop_path_cn):
                    return desktop_path_cn

        # 方法4：如果是打包的exe文件，使用exe所在目录
        if getattr(sys, 'frozen', False):
            # 打包后的exe文件
            exe_dir = os.path.dirname(sys.executable)
            return exe_dir

        # 方法5：回退到当前工作目录
        return os.getcwd()

    except Exception as e:
        # 静默处理错误，最终回退到当前目录
        return os.getcwd()


def getsetime():
    """
    获取默认的开始时间和结束时间
    返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"

    Returns:
        tuple: (开始日期, 结束日期) 格式为 "YYYY-MM-DD"
    """
    from datetime import datetime, timedelta
    current_date = datetime.now()
    current_weekday = current_date.weekday()
    previous_friday_index = current_weekday - 4
    if previous_friday_index < 0:
        previous_friday_index += 7
    previous_friday = current_date - timedelta(days=previous_friday_index)
    previous_six_days = previous_friday - timedelta(days=6)
    # 返回前一个星期五及其前六天的日期，格式为"YYYY-MM-DD"
    return previous_six_days.strftime("%Y-%m-%d"), previous_friday.strftime("%Y-%m-%d")


def extract_cus_ciq_nos(data):
    """
    从数据中提取所有的cusCiqNo

    :param data: 报关单数据（JSON格式）
    :return: cusCiqNo列表
    """
    try:
        # 解析数据
        if isinstance(data, str):
            data = json.loads(data)

        rows = data.get('rows', [])
        if not rows:
            return []

        # 提取所有的cusCiqNo
        cus_ciq_nos = []
        for row in rows:
            cus_ciq_no = row.get('cusCiqNo', '')
            if cus_ciq_no and cus_ciq_no not in cus_ciq_nos:
                cus_ciq_nos.append(cus_ciq_no)

        return cus_ciq_nos

    except Exception as e:
        # 静默处理错误，返回空列表
        return []


def extract_seg_content(text):
    """
    从文本中提取 WUW 或 SEG 开头的完整合同协议号
    兼容以下格式：
    - WUWPO2824032030001-10
    - WUWPO3224020430003
    - WUW2024010603-2（2）
    - WUW-PO9524041730001
    - SEG2024041001-13（1）
    - SEG2025020701-11（1）

    参数:
        text (str): 输入的文本字符串

    返回:
        list: 包含所有匹配的完整合同协议号列表
    """
    # 匹配 WUW 或 SEG 开头的完整合同协议号
    pattern = r'(?:WUW|SEG)[-A-Za-z]*\d+(?:-\d+)?(?:[()（）]\d+[()（）])*'

    # 查找所有匹配项
    matches = re.findall(pattern, text)
    if not matches or len(matches) < 1:
        return ""
    return "; ".join(matches)


def get_pdf_files(folder_path):
    """获取文件夹中的所有PDF文件"""
    p = Path(folder_path)
    return list(p.glob('*.pdf'))


def rename_pdf_file(file_path, new_name):
    """
    使用 pathlib 修改文件名
    :param file_path: 原文件路径
    :param new_name: 新文件名
    """
    old_file = Path(file_path)
    if not old_file.exists():
        return False

    # 生成新路径（保持原目录）
    new_file = old_file.parent / new_name
    if new_file.exists():
        return False

    old_file.rename(new_file)
    return True


def get_pdf(cusCiqNo, page, pdfpath, progress_callback=None):
    """
    下载pdf
    :param cusCiqNo: 获取所有符合条件的统一编号
    :param page: 页面对象
    :param pdfpath: PDF保存路径
    :param progress_callback: 进度回调函数
    :return: None
    """
    if progress_callback:
        progress_callback(f"📄 需要下载 {len(cusCiqNo)} 个PDF文件")
        progress_callback(f"📁 PDF保存路径: {pdfpath}")

    ourl = "https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/printCluster/entries/ftl/1/0/0/"

    # 遍历每个统一编号
    for index, item in enumerate(cusCiqNo, 1):
        # 构建下载URL
        url = ourl + item + ".pdf"

        if progress_callback:
            progress_callback(f"📥 正在下载第 {index}/{len(cusCiqNo)} 个PDF: {item}")

        try:
            # 下载PDF文件到指定路径
            page.download(url, goal_path=pdfpath, timeout=20)
            if progress_callback:
                progress_callback(f"  ✅ 下载成功: {item}.pdf")
        except Exception as e:
            if progress_callback:
                progress_callback(f"  ❌ 下载失败: {item}.pdf - {str(e)}")

        # 等待1秒，以避免频繁下载
        time.sleep(1)

    if progress_callback:
        progress_callback(f"📄 PDF下载完成，共处理 {len(cusCiqNo)} 个文件")
    # 等待2秒，以确保所有文件下载完成
    time.sleep(2)


def get_api_headers(account_manager):
    """
    获取当前可用的API请求头

    :param account_manager: 账号管理器实例
    :return: API请求头字典，如果没有可用密钥返回None
    """
    api_key = account_manager.get_current_api_key()
    if not api_key:
        return None

    return {
        'x-ti-app-id': api_key.get('app_id'),
        'x-ti-secret-code': api_key.get('secret_code'),
        'content-type': 'application/x-www-form-urlencoded',
    }


def call_recognition_api(pdf_data, account_manager, progress_callback=None):
    """
    调用PDF识别API，支持自动切换密钥

    :param pdf_data: PDF文件数据
    :param account_manager: 账号管理器实例
    :param progress_callback: 进度回调函数
    :return: API响应结果，失败返回None
    """
    max_retries = 3  # 最多重试3次（切换3个不同的API密钥）

    for retry in range(max_retries):
        try:
            # 获取当前API密钥
            headers = get_api_headers(account_manager)
            if not headers:
                if progress_callback:
                    progress_callback("❌ 没有可用的API密钥")
                return None

            current_api_key = account_manager.get_current_api_key()
            app_id = current_api_key.get('app_id')

            if progress_callback and retry > 0:
                progress_callback(f"🔄 使用API密钥: {app_id} (第{retry+1}次尝试)")

            # 调用API
            response = requests.post(
                PDF_RECOGNITION_API_URL,
                headers=headers,
                data=pdf_data,
                verify=False,
                proxies={"http": None, "https": None},
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()

                # 检查是否余额不足
                if result.get('code') == 40003:
                    if progress_callback:
                        progress_callback(f"⚠️ API密钥 {app_id} 余额不足，尝试切换...")

                    # 标记当前密钥为余额不足
                    account_manager.mark_api_key_exhausted(app_id)

                    # 如果还有重试机会，继续下一次循环
                    if retry < max_retries - 1:
                        continue
                    else:
                        if progress_callback:
                            progress_callback("❌ 所有API密钥都已用完")
                        return None

                # 更新API密钥使用记录
                account_manager.update_api_key_usage(app_id)
                return result

            else:
                if progress_callback:
                    progress_callback(f"⚠️ API调用失败，状态码: {response.status_code}")
                return None

        except Exception as e:
            if progress_callback:
                progress_callback(f"⚠️ API调用出错: {str(e)}")

            # 如果还有重试机会，尝试切换API密钥
            if retry < max_retries - 1:
                account_manager.switch_to_next_api_key()
                continue
            else:
                return None

    return None


def recognize_and_rename_pdfs(pdf_path, account_dir, account_manager, progress_callback=None):
    """
    识别PDF文件并重命名，同时生成Excel报告

    :param pdf_path: PDF文件夹路径
    :param account_dir: 账号目录路径
    :param account_manager: 账号管理器实例
    :param progress_callback: 进度回调函数
    :return: 是否成功
    """
    try:
        if progress_callback:
            progress_callback("🔍 开始识别PDF文件...")

        # 检查是否有可用的API密钥
        if not account_manager.get_current_api_key():
            if progress_callback:
                progress_callback("❌ 没有可用的API密钥，请先添加API密钥")
            return False

        files = get_pdf_files(pdf_path)
        if not files:
            if progress_callback:
                progress_callback("⚠️ 未找到PDF文件")
            return False

        if progress_callback:
            progress_callback(f"📄 找到 {len(files)} 个PDF文件，开始识别...")

        all_data = []

        for index, file in enumerate(files):
            try:
                if progress_callback:
                    progress_callback(f"🔍 正在识别第 {index + 1}/{len(files)} 个文件: {file.name}")

                # 读取PDF文件
                with open(file, 'rb') as f:
                    data = f.read()

                # 调用识别API
                str_json = call_recognition_api(data, account_manager, progress_callback)

                if not str_json:
                    if progress_callback:
                        progress_callback(f"⚠️ 识别API调用失败: {file.name}")
                    continue
                if 'result' not in str_json or 'pages' not in str_json['result']:
                    if progress_callback:
                        progress_callback(f"⚠️ 识别结果格式异常: {file.name}")
                    continue

                pages = str_json['result']['pages']
                if not pages or not pages[0].get('object_list'):
                    if progress_callback:
                        progress_callback(f"⚠️ 未识别到有效内容: {file.name}")
                    continue

                # 提取基本信息
                str_json_obj = pages[0]['object_list'][0]
                details = str_json_obj['details']

                contract_agreement_number = details.get('contract_agreement_number', {}).get('value', '')
                declaration_date = details.get('declaration_date', {}).get('value', '')
                overseas_consignee = details.get('overseas_consignee', {}).get('value', '')
                domestic_consignor = details.get('domestic_consignor', {}).get('value', '')
                customs_number = details.get('customs_number', {}).get('value', '')
                delivery_number = details.get('delivery_number', {}).get('value', '')
                destination_country = details.get('destination_country', {}).get('value', '')
                marking_marks_and_remarks = details.get('marking_marks_and_remarks', {}).get('value', '')

                # 提取合同协议号集合
                marking_marks = extract_seg_content(marking_marks_and_remarks)

                # 提取提单号
                pattern = r"提单号：([A-Z0-9]+)"
                match = re.search(pattern, marking_marks_and_remarks)
                bill_of_lading = match.group(1) if match else ""

                # 处理商品信息
                product_name_list = []
                total_price_count = 0

                for page in pages:
                    object_list = page.get('object_list', [])
                    if not object_list:
                        continue

                    items = object_list[0].get('details', {}).get('item_list', [])
                    for item in items:
                        try:
                            product_id = item.get('product_id', {}).get('value', '')
                            item_number = item.get('item_number', {}).get('value', '')

                            product_name_full = item.get('product_name_and_specification_model', {}).get('value', '')
                            product_name = product_name_full.split("\n")[0] if product_name_full else ''
                            product_name_list.append(product_name)

                            product_specification = "｜".join(
                                product_name_full.split("\n")[1:]).replace("＊", "*") if product_name_full else ''

                            quantity_and_unit_full = item.get('quantity_and_unit', {}).get('value', '')
                            quantity_and_unit = re.sub(r'\D', "", quantity_and_unit_full.split("\n")[-1]) if quantity_and_unit_full else ''

                            price_info = item.get('unit_price_total_price_currency_value', {}).get('value', '')
                            unit_price = price_info.split("\n")[0] if price_info else ''
                            total_price = price_info.split("\n")[1] if len(price_info.split("\n")) > 1 else ''

                            if total_price:
                                try:
                                    total_price_count += float(total_price)
                                except:
                                    pass

                            unit = UNIT_DICT.get(product_name, "件")

                            all_data.append([
                                item_number, product_id, product_name, product_specification,
                                quantity_and_unit, unit, unit_price, total_price, customs_number,
                                domestic_consignor, declaration_date, overseas_consignee,
                                contract_agreement_number, marking_marks, delivery_number,
                                destination_country, bill_of_lading
                            ])
                        except Exception as item_error:
                            if progress_callback:
                                progress_callback(f"⚠️ 处理商品信息时出错: {str(item_error)}")

                # 生成新文件名
                unique_list = list(set(product_name_list))
                product_name_str = "+".join(unique_list)
                new_filename = f"{index+1}_{contract_agreement_number}_{product_name_str}_{customs_number}_{total_price_count}.pdf"
                new_filename = new_filename.replace('/', '').replace('\\', '').replace(':', '').replace('*', '').replace('?', '').replace('"', '').replace('<', '').replace('>', '').replace('|', '')

                # 重命名文件
                if rename_pdf_file(file, new_filename):
                    if progress_callback:
                        progress_callback(f"✅ 文件重命名成功: {new_filename}")
                else:
                    if progress_callback:
                        progress_callback(f"⚠️ 文件重命名失败: {file.name}")

            except Exception as file_error:
                if progress_callback:
                    progress_callback(f"❌ 处理文件 {file.name} 时出错: {str(file_error)}")
                continue

        # 生成Excel报告
        if all_data:
            columns = [
                '项号', '商品编号', '商品名称', '商品名称及规格型号', '数量', '单位',
                '单价', '总价', '海关编号', '境内发货人', '申报日期', '境外收货人',
                '合同协议号', '合同协议号集合', '提运单号', '运抵国', '提单号'
            ]

            df = pd.DataFrame(all_data, columns=columns)
            output_excel_path = os.path.join(account_dir, "PDF识别结果.xlsx")
            df.to_excel(output_excel_path, index=False)

            if progress_callback:
                progress_callback(f"✅ Excel报告已生成: {output_excel_path}")

        if progress_callback:
            progress_callback("🎉 PDF识别和重命名完成！")

        # 生成PDF文件名列表Excel
        if progress_callback:
            progress_callback("📋 正在生成PDF文件名列表...")

        try:
            # 从account_dir中提取账号名称
            account_name = os.path.basename(account_dir).split('-')[0] if account_dir else "未知账号"

            # 调用排序和导出函数
            df_result = sort_and_export_to_excel(pdf_path, account_dir, account_name, progress_callback)

            if df_result is not None:
                if progress_callback:
                    progress_callback("✅ PDF文件名列表已生成")
            else:
                if progress_callback:
                    progress_callback("⚠️ PDF文件名列表生成失败，但不影响主要功能")

        except Exception as list_error:
            if progress_callback:
                progress_callback(f"⚠️ 生成PDF文件名列表时出错: {str(list_error)}")

        return True

    except Exception as e:
        if progress_callback:
            progress_callback(f"❌ PDF识别过程出错: {str(e)}")
        return False


def split_date_range(date_range, step_days=7):
    """
    title: 日期范围分块工具
    description: 将给定的日期范围按指定天数进行分块，返回包含每个分块起止日期的列表。
    inputs:
        - date_range (str): 日期范围字符串，格式为"yyyy-mm-dd--yyyy-mm-dd"，eg: "2025-04-27--2025-05-28"
        - step_days (int): 每个分块的天数，默认为7天，eg: 7
    outputs:
        - date_blocks (list): 包含日期分块的列表，eg: [{"start_date":"2025-04-27","end_date":"2025-05-04"}]
    """
    try:
        # 解析日期范围
        start_date_str, end_date_str = date_range.split('--')
        start_date = datetime.datetime.strptime(start_date_str, '%Y-%m-%d').date()
        end_date = datetime.datetime.strptime(end_date_str, '%Y-%m-%d').date()

        # 初始化结果列表和当前块的起始日期
        date_blocks = []
        current_start = start_date

        # 分块处理日期范围
        while current_start <= end_date:
            # 计算当前块的结束日期
            current_end = min(current_start + datetime.timedelta(days=step_days - 1), end_date)

            # 添加当前块到结果列表
            date_blocks.append({
                "start_date": current_start.strftime('%Y-%m-%d'),
                "end_date": current_end.strftime('%Y-%m-%d')
            })

            # 移动到下一个块的起始日期
            current_start = current_start + datetime.timedelta(days=step_days)

        return date_blocks

    except Exception as e:
        # 如果解析失败，返回原始日期范围作为单个块
        try:
            start_date_str, end_date_str = date_range.split('--')
            return [{
                "start_date": start_date_str,
                "end_date": end_date_str
            }]
        except:
            return []


def extract_prefix_number(filename):
    """
    提取文件名开头的数字部分

    Parameters:
    -----------
    filename : str
        文件名（如 "1_合同号_商品名_海关编号_总价.pdf"）

    Returns:
    --------
    int
        提取的前缀数字
    """
    try:
        # 提取开头的数字部分（如 "1_" → 1）
        prefix = filename.split('_')[0]
        return int(prefix)
    except (ValueError, IndexError):
        # 如果无法提取数字，返回一个很大的数，让它排在最后
        return 999999


def sort_and_export_to_excel(pdf_path, excel_save_path, account_name, progress_callback=None):
    """
    按文件名前缀数字排序，并导出到 Excel

    Parameters:
    -----------
    pdf_path : str
        PDF文件夹路径
    excel_save_path : str
        Excel文件保存路径
    account_name : str
        账号名称
    progress_callback : function
        进度回调函数

    Returns:
    --------
    pd.DataFrame
        排序后的文件名 DataFrame
    """
    try:
        if progress_callback:
            progress_callback("📋 开始整理PDF文件名列表...")

        # 获取PDF文件夹中的所有PDF文件
        pdf_files = get_pdf_files(pdf_path)

        if not pdf_files:
            if progress_callback:
                progress_callback("⚠️ PDF文件夹中没有找到PDF文件")
            return None

        # 提取文件名（不包含路径）
        file_names = [file.name for file in pdf_files]

        if progress_callback:
            progress_callback(f"📄 找到 {len(file_names)} 个PDF文件")

        # 按前缀数字排序
        sorted_file_names = sorted(file_names, key=extract_prefix_number)

        if progress_callback:
            progress_callback("🔢 按前缀数字排序完成")

        # 去掉.pdf后缀，保留完整的重命名信息
        processed_names = [name.replace('.pdf', '') for name in sorted_file_names]

        # 创建DataFrame，只保留完整文件名列
        data = []
        for name in processed_names:
            data.append({'完整文件名': name})

        # 创建DataFrame
        df = pd.DataFrame(data)

        # 生成Excel文件路径
        excel_filename = f'{account_name}-PDF文件名列表.xlsx'
        excel_path = os.path.join(excel_save_path, excel_filename)

        # 导出到Excel
        df.to_excel(excel_path, index=False, sheet_name='PDF文件列表')

        if progress_callback:
            progress_callback(f"✅ PDF文件名列表已保存到: {excel_filename}")

        return df

    except Exception as e:
        error_msg = f"❌ 整理PDF文件名列表失败: {str(e)}"
        if progress_callback:
            progress_callback(error_msg)
        return None



def create_account_directory(base_path, account_name, start_date, end_date):
    """
    创建账号相关的目录结构
    格式：基础路径/账号/账号-开始时间-结束时间/

    :param base_path: 基础保存路径
    :param account_name: 账号名称
    :param start_date: 开始日期 (YYYY-MM-DD)
    :param end_date: 结束日期 (YYYY-MM-DD)
    :return: 创建的目录路径
    """
    try:
        # 第一层：账号目录
        account_base_dir = os.path.join(base_path, account_name)
        os.makedirs(account_base_dir, exist_ok=True)

        # 第二层：账号-开始时间-结束时间目录
        date_dir_name = f"{account_name}-{start_date}-{end_date}"
        account_dir = os.path.join(account_base_dir, date_dir_name)
        os.makedirs(account_dir, exist_ok=True)

        # 第三层：PDF子目录
        pdf_dir = os.path.join(account_dir, "pdf")
        os.makedirs(pdf_dir, exist_ok=True)

        return account_dir, pdf_dir

    except Exception as e:
        raise Exception(f"创建账号目录失败: {str(e)}")


def export_to_excel(data, filename=None, save_path=None, account_name=None, start_date=None, end_date=None):
    """
    将报关单数据导出到Excel文件

    :param data: 报关单数据（JSON格式）
    :param filename: 输出文件名（可选）
    :param save_path: 保存路径（可选）
    :param account_name: 账号名称（用于创建目录）
    :param start_date: 开始日期（用于创建目录）
    :param end_date: 结束日期（用于创建目录）
    :return: 导出的文件路径
    """
    try:
        # 解析数据
        if isinstance(data, str):
            data = json.loads(data)

        rows = data.get('rows', [])
        if not rows:
            raise ValueError("没有数据可导出")

        # 创建DataFrame
        export_data = []
        for row in rows:
            mapped_row = {}
            for chinese_name, field_name in FIELD_MAPPING.items():
                # 获取字段值，如果不存在则为空字符串
                value = row.get(field_name, '')
                # 处理None值
                if value is None:
                    value = ''
                mapped_row[chinese_name] = value
            export_data.append(mapped_row)

        df = pd.DataFrame(export_data)

        # 生成文件名
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"报关单数据_{timestamp}.xlsx"

        # 处理保存路径 - 如果提供了账号信息，创建账号目录
        if save_path and account_name and start_date and end_date:
            account_dir, _ = create_account_directory(save_path, account_name, start_date, end_date)
            full_path = os.path.join(account_dir, filename)
        elif save_path and os.path.exists(save_path):
            full_path = os.path.join(save_path, filename)
        else:
            full_path = filename

        # 导出到Excel
        df.to_excel(full_path, index=False, engine='openpyxl')

        # 应用Excel样式
        apply_excel_style(full_path)

        return full_path

    except Exception as e:
        raise Exception(f"Excel导出失败: {str(e)}")

class LoginThread(QThread):
    """登录线程，用于执行DrissionPage登录操作"""

    # 定义信号
    login_success = pyqtSignal(str)  # 登录成功信号
    login_failed = pyqtSignal(str)   # 登录失败信号
    login_progress = pyqtSignal(str) # 登录进度信号
    data_received = pyqtSignal(str)  # 数据接收信号
    
    def __init__(self, account_info, start_date="", end_date="", account_name="", account_manager=None, block_days=7):
        super().__init__()
        self.account_info = account_info
        self.start_date = start_date
        self.end_date = end_date
        self.account_name = account_name
        self.account_manager = account_manager
        self.block_days = block_days
    
    def run(self):
        """执行登录操作"""
        try:
            self.login_progress.emit("正在初始化浏览器...")
            


            # 获取账号信息
            password = self.account_info.get('password', '')
            account_name = self.account_name

            self.login_progress.emit(f"正在使用账号 {account_name} 登录...")
            self.login_progress.emit(f"查询日期范围: {self.start_date} 到 {self.end_date}")

            # 执行登录逻辑
            page = self.login_to_system(password)

            if page:
                self.login_success.emit(f"✅ 账号 {account_name} 登录成功！已跳转到报关数据查询页面")

                # 获取浏览器cookies并进行数据查询
                self.fetch_data_with_cookies(page, self.start_date, self.end_date)
            else:
                self.login_failed.emit(f"❌ 账号 {account_name} 登录失败")
            
        except Exception as e:
            error_msg = f"❌ 登录过程中发生错误: {str(e)}"
            self.login_failed.emit(error_msg)

    def login_to_system(self, password):
        """
        登录并跳转到报关数据查询
        """
        max_retries = 3  # 增加重试次数
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.login_progress.emit(f"🔄 第 {attempt + 1} 次登录尝试...")
                else:
                    self.login_progress.emit(f"🚀 开始第 {attempt + 1} 次登录尝试...")

                result = self._perform_login(password, attempt + 1)
                if result:
                    self.login_progress.emit(f"✅ 第 {attempt + 1} 次登录尝试成功！")
                    return result

            except Exception as e:
                error_msg = str(e)
                self.login_progress.emit(f"❌ 第 {attempt + 1} 次登录尝试失败: {error_msg}")

                # 根据错误类型提供更具体的建议
                if "登录状态异常" in error_msg:
                    self.login_progress.emit("💡 可能原因：页面加载缓慢或网络延迟，将自动重试")
                elif "未找到" in error_msg:
                    self.login_progress.emit("💡 可能原因：页面元素加载不完整，将自动重试")
                elif "网络" in error_msg or "连接" in error_msg:
                    self.login_progress.emit("💡 可能原因：网络连接问题，将自动重试")

            # 重试逻辑
            if attempt < max_retries - 1:
                wait_time = 8 + (attempt * 2)  # 递增等待时间：8, 10, 12, 14, 16秒
                self.login_progress.emit(f"⏳ 等待{wait_time}秒后进行第 {attempt + 2} 次重试...")
                time.sleep(wait_time)
            else:
                self.login_progress.emit(f"❌ 已达到最大重试次数({max_retries})，登录失败")
                self.login_progress.emit("💡 建议：请检查网络连接、密码是否正确，或稍后再试")
                return None

        return None

    def _perform_login(self, password, attempt_num):
        """
        执行单次登录尝试
        """
        page = None
        temp_dir = None
        try:
            # 创建ChromiumOptions配置
            self.login_progress.emit("正在配置浏览器选项（无痕模式）...")
            co = ChromiumOptions()

            # 启用无痕模式，避免账号间相互影响
            co.incognito(True)  # 启用无痕模式

            # 其他浏览器选项
            co.set_argument('--disable-web-security')  # 禁用web安全
            co.set_argument('--disable-features=VizDisplayCompositor')  # 禁用某些功能
            co.set_argument('--no-sandbox')  # 禁用沙盒模式
            co.set_argument('--disable-dev-shm-usage')  # 禁用/dev/shm使用
            co.set_argument('--disable-blink-features=AutomationControlled')  # 禁用自动化控制检测

            # 每次都使用新的用户数据目录，确保完全隔离
            temp_dir = tempfile.mkdtemp(prefix=f"chrome_profile_{uuid.uuid4().hex[:8]}_")
            co.set_user_data_path(temp_dir)

            self.login_progress.emit(f"使用临时用户目录: {temp_dir}")
            self.login_progress.emit("✅ 无痕模式已启用，账号间完全隔离")

            # 创建ChromiumPage对象
            self.login_progress.emit("正在创建浏览器页面...")
            page = ChromiumPage(addr_or_opts=co)

            # 访问中国国际贸易单一窗口
            http_path = 'https://app.singlewindow.cn/cas/login?service=https%3A%2F%2Fsz.singlewindow.cn%2Fdyck%2FswProxy%2Fdeskserver%2Fsw%2FdeskIndex%3Fmenu_id%3Ddec001'  # 登录页面地址
            self.login_progress.emit(f"正在访问: {http_path}")
            page.get(http_path)

            # 等待页面加载
            time.sleep(3)

            current_title = page.title
            current_url = page.url
            self.login_progress.emit(f"页面标题: {current_title}")
            self.login_progress.emit(f"页面URL: {current_url}")

            # 检查是否成功访问登录页面
            if "登录" in current_title or "login" in current_url.lower():
                # 等待并点击卡介质密码登录的按钮
                self.login_progress.emit("正在切换到卡介质密码登录...")
                try:
                    card_tab_btn = page.ele("@id=cardTabBtn", timeout=10)
                    if card_tab_btn:
                        card_tab_btn.click()
                        self.login_progress.emit("✅ 成功切换到卡介质密码登录")
                    else:
                        raise Exception("未找到卡介质密码登录按钮")
                except Exception as e:
                    self.login_progress.emit(f"⚠️ 切换登录方式失败: {str(e)}")
                    raise

                # 等待并输入卡介质密码
                self.login_progress.emit("正在输入卡介质密码...")
                try:
                    password_input = page.ele("@id=password", timeout=10)
                    if password_input:
                        password_input.clear()  # 先清空
                        password_input.input(password)
                        self.login_progress.emit("✅ 密码输入完成")
                    else:
                        raise Exception("未找到密码输入框")
                except Exception as e:
                    self.login_progress.emit(f"⚠️ 密码输入失败: {str(e)}")
                    raise

                # 等待并点击 Intel 选项框
                self.login_progress.emit("正在选择Intel选项...")
                try:
                    intel_checkbox = page.ele("@id=checkboxIntel", timeout=10)
                    if intel_checkbox:
                        intel_checkbox.click()
                        self.login_progress.emit("✅ Intel选项已选择")
                    else:
                        raise Exception("未找到Intel选项框")
                except Exception as e:
                    self.login_progress.emit(f"⚠️ Intel选项选择失败: {str(e)}")
                    raise

                # 等待并点击登录按钮
                self.login_progress.emit("正在点击登录按钮...")
                try:
                    login_button = page.ele("@id=loginbutton", timeout=10)
                    if login_button:
                        login_button.click()
                        self.login_progress.emit("✅ 登录按钮已点击")
                    else:
                        raise Exception("未找到登录按钮")
                except Exception as e:
                    self.login_progress.emit(f"⚠️ 点击登录按钮失败: {str(e)}")
                    raise

                time.sleep(5)
                # 验证登录状态
                self._verify_login_status(page, attempt_num)

            else:
                self.login_progress.emit("⚠️ 未检测到登录页面，可能网络连接有问题")
                raise Exception("无法访问登录页面")

            return page

        except Exception as e:
            error_msg = f"登录执行失败: {e}"
            self.login_progress.emit(error_msg)

            # 提供详细的错误信息和建议
            if "网络" in str(e) or "连接" in str(e):
                self.login_progress.emit("💡 建议：检查网络连接是否正常")
            elif "元素" in str(e) or "未找到" in str(e):
                self.login_progress.emit("💡 建议：页面可能加载不完整，稍后重试")
            elif "密码" in str(e):
                self.login_progress.emit("💡 建议：检查密码是否正确")
            else:
                self.login_progress.emit("💡 建议：稍后重试，或检查网站是否正常")

            # 记录当前页面状态用于调试
            if page:
                try:
                    current_url = page.url
                    current_title = page.title
                    self.login_progress.emit(f"🔍 失败时页面状态 - URL: {current_url}, 标题: {current_title}")
                except:
                    pass

            # 清理资源
            self._cleanup_login_resources(page, temp_dir)

            # 重新抛出异常以触发重试机制
            raise e

        finally:
            # 注意：这里不清理资源，因为成功时page对象还在使用中
            # 清理工作会在数据获取完成后或异常时进行
            pass

    def _verify_login_status(self, page, attempt_num):
        """
        验证登录状态的多重检查方法
        """
        self.login_progress.emit(f"等待登录成功确认（第{attempt_num}次尝试）...")


        # 多重验证登录状态
        login_success = False

        try:
            # 方法2：检查页面标题
            current_title = page.title
            self.login_progress.emit(f"当前页面标题: {current_title}")

            if "单一窗口" in current_title and "登录" not in current_title:
                self.login_progress.emit("✅ 检测到页面标题变化，登录可能成功")
                login_success = True

            # 方法3：检查企业操作员标志元素（主要验证方法）
            self.login_progress.emit("🔍 检查企业操作员标志...")
            try:
                success_element = page.ele("@id=accountTypeCn", timeout=15)
                if success_element:
                    element_text = success_element.text.strip()
                    self.login_progress.emit(f"找到accountTypeCn元素，内容: '{element_text}'")
                    if element_text == "企业操作员":
                        self.login_progress.emit("✅ 检测到登录成功标志：企业操作员")
                        login_success = True
                    else:
                        self.login_progress.emit(f"⚠️ 企业操作员标志不匹配，实际内容: '{element_text}'")
                else:
                    self.login_progress.emit("⚠️ 未找到accountTypeCn元素")
            except Exception as element_error:
                self.login_progress.emit(f"⚠️ 检查企业操作员标志时出错: {str(element_error)}")

            # 最终验证
            if login_success:
                self.login_progress.emit("✅ 登录状态验证通过")
                time.sleep(3)  # 额外等待确保页面完全加载
            else:
                # 所有验证方法都失败
                error_msg = f"登录状态异常，未检测到企业操作员标志"
                self.login_progress.emit(f"⚠️ {error_msg}")
                raise Exception(error_msg)

        except Exception as e:
            if "登录状态异常" in str(e):
                # 重新抛出验证失败异常，触发重试
                raise e
            else:
                # 其他异常（如超时），也触发重试
                error_msg = f"登录确认过程出错: {str(e)}"
                self.login_progress.emit(f"⚠️ {error_msg}")
                raise Exception(error_msg)

    def _cleanup_login_resources(self, page, temp_dir):
        """
        清理登录过程中创建的资源
        """
        if page:
            try:
                page.quit()
                self.login_progress.emit("🧹 浏览器页面已关闭")
            except Exception as e:
                self.login_progress.emit(f"⚠️ 关闭浏览器页面时出错: {str(e)}")

        # 清理临时目录（可选，通常系统会自动清理）
        if temp_dir:
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                self.login_progress.emit(f"🧹 临时目录已清理: {temp_dir}")
            except Exception as e:
                self.login_progress.emit(f"⚠️ 清理临时目录时出错: {str(e)}")

    def create_custom_query(self, start_date=None, end_date=None, table_flag=None, ie_flag=None, etps_category=None):
        """
        创建自定义查询参数

        :param start_date: 开始时间
        :param end_date: 结束时间
        :param table_flag: 表格标志 (使用 TableFlag 枚举)
        :param ie_flag: 进出口标志 (使用 IEFlag 枚举)
        :param etps_category: 企业类型 (使用 EtpsCategory 枚举)
        :return: 编码后的查询参数
        """

        # 如果没有指定日期，使用传入的日期
        if not start_date:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")

        # 使用枚举设置默认值
        if table_flag is None:
            table_flag = TableFlag.YES.value  # 默认选择是
        if ie_flag is None:
            ie_flag = IEFlag.EXPORT.value  # 默认出口
        if etps_category is None:
            etps_category = EtpsCategory.COMPANY.value  # 默认企业

        # 定义数据字典
        data = {
            "cusCiqNoHidden": "",
            "dclTrnRelFlagHidden": "",
            "transPreNoHidden": "",
            "cusIEFlagHidden": "",
            "cusOrgCode": "",
            "dclTrnRelFlag": "0",
            "cusDecStatus": "",
            "etpsCategory": etps_category,
            "cusIEFlag": ie_flag,
            "entryId": "",
            "cusCiqNo": "",
            "cnsnTradeCode": "",
            "billNo": "",
            "customMaster": "",
            "tableFlag": table_flag,
            "updateTime": start_date,
            "updateTimeEnd": end_date,
            "operateDate": "1",
            "verifyCode": "",
            "queryPage": "cusBasicQuery",
            "operType": "0"
        }
        # 将数据转换为JSON字符串
        json_str = json.dumps(data)

        # 执行两次encodeURI操作
        encoded_once = urllib.parse.quote(json_str, safe='~()*!.\'')
        encoded_twice = urllib.parse.quote(encoded_once, safe='~()*!.\'')

        return encoded_twice

    def fetch_data_with_cookies(self, page, start_date, end_date):
        """使用浏览器cookies获取数据"""
        try:
            self.login_progress.emit("正在获取浏览器cookies...")

            # 获取浏览器cookies - 使用正确的DrissionPage API
            try:
                # 使用正确的 cookies() 方法
                cookies = page.cookies()

                # 转换cookies格式为requests可用的格式
                cookies_dict = {}

                # DrissionPage的CookiesList可以直接迭代
                for cookie in cookies:
                    # 每个cookie应该有name和value属性
                    if hasattr(cookie, 'name') and hasattr(cookie, 'value'):
                        cookies_dict[cookie.name] = cookie.value
                    elif isinstance(cookie, dict):
                        cookies_dict[cookie.get('name', '')] = cookie.get('value', '')

                self.login_progress.emit(f"转换后的cookies数量: {len(cookies_dict)}")

                # 如果没有获取到cookies，直接报错
                if not cookies_dict:
                    raise Exception("未获取到有效的cookies")

            except Exception as cookie_error:
                self.login_progress.emit(f"❌ 获取cookies失败: {str(cookie_error)}")
                return  # 直接返回，不继续执行

            # 检查日期范围是否需要分块查询
            date_range_str = f"{start_date}--{end_date}"
            date_blocks = split_date_range(date_range_str, step_days=self.block_days)  # 使用设置的分块天数

            if len(date_blocks) > 1:
                self.login_progress.emit(f"📅 日期范围较大，将分为 {len(date_blocks)} 个时间块进行查询")
                for i, block in enumerate(date_blocks, 1):
                    self.login_progress.emit(f"  📅 时间块 {i}: {block['start_date']} 到 {block['end_date']}")
            else:
                self.login_progress.emit(f"📅 查询日期范围: {start_date} 到 {end_date}")

            # 设置请求头
            headers = {
                'Accept': 'application/json, text/javascript, */*; q=0.01',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Cache-Control': 'no-cache',
                'Connection': 'keep-alive',
                'Content-Type': 'application/json',
                'Pragma': 'no-cache',
                'Referer': 'https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/cusQueryZhNew?ngBasePath=https%3A%2F%2Fsz.singlewindow.cn%3A443%2Fdyck%2FswProxy%2Fdecserver%2F',
                'Sec-Fetch-Dest': 'empty',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Site': 'same-origin',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                'X-Requested-With': 'XMLHttpRequest',
                'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Google Chrome";v="138"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
            }

            # 执行分块查询并合并结果
            all_merged_data = self.execute_block_query(date_blocks, headers, cookies_dict)

            # 处理双重查询结果
            if all_merged_data:
                self.login_progress.emit("✅ 双重查询数据获取成功，正在处理数据...")
                self.data_received.emit(json.dumps(all_merged_data, ensure_ascii=False))

                # 在数据获取成功后，立即下载PDF
                try:
                    self.login_progress.emit("📄 正在提取统一编号...")
                    cus_ciq_nos = extract_cus_ciq_nos(all_merged_data)

                    if cus_ciq_nos:
                        self.login_progress.emit(f"✅ 提取到 {len(cus_ciq_nos)} 个统一编号")

                        # 创建账号相关的目录结构
                        base_save_path = get_default_save_path()  # 使用桌面作为默认路径
                        account_dir, pdf_path = create_account_directory(
                            base_save_path, self.account_name, self.start_date, self.end_date
                        )
                        self.login_progress.emit(f"📁 账号目录: {account_dir}")
                        self.login_progress.emit(f"📁 PDF保存目录: {pdf_path}")

                        self.login_progress.emit("📄 开始下载PDF文件...")
                        get_pdf(cus_ciq_nos, page, pdf_path, self.login_progress.emit)
                        self.login_progress.emit("✅ PDF下载完成")

                        # 下载完成后进行PDF识别和重命名
                        self.login_progress.emit("🔍 开始识别和重命名PDF文件...")
                        try:
                            success = recognize_and_rename_pdfs(pdf_path, account_dir, self.account_manager, self.login_progress.emit)
                            if success:
                                self.login_progress.emit("🎉 PDF识别和重命名完成！")
                            else:
                                self.login_progress.emit("⚠️ PDF识别过程中出现问题，但不影响主要功能")
                        except Exception as recognition_error:
                            self.login_progress.emit(f"⚠️ PDF识别出错: {str(recognition_error)}")

                    else:
                        self.login_progress.emit("⚠️ 未找到统一编号，跳过PDF下载")

                except Exception as pdf_error:
                    self.login_progress.emit(f"❌ PDF下载失败: {str(pdf_error)}")

            else:
                self.login_progress.emit("❌ 未获取到任何数据")

        except Exception as e:
            self.login_progress.emit(f"❌ 数据获取过程中发生错误: {str(e)}")

        finally:
            # 数据获取完成后，清理浏览器和临时目录
            self.cleanup_browser_session(page)

    def execute_block_query(self, date_blocks, headers, cookies_dict):
        """
        执行分块查询，对每个时间块进行双重查询并合并所有结果

        :param date_blocks: 日期分块列表
        :param headers: 请求头
        :param cookies_dict: cookies字典
        :return: 合并后的完整数据
        """
        try:
            all_final_data = []
            total_records_all_blocks = 0

            # 遍历每个时间块
            for block_index, date_block in enumerate(date_blocks, 1):
                block_start = date_block['start_date']
                block_end = date_block['end_date']

                self.login_progress.emit(f"🔍 开始处理时间块 {block_index}/{len(date_blocks)}: {block_start} 到 {block_end}")

                # 对当前时间块执行双重查询
                block_data = self.execute_dual_query(block_start, block_end, headers, cookies_dict)

                if block_data and block_data.get('rows'):
                    block_records = len(block_data['rows'])
                    total_records_all_blocks += block_records
                    all_final_data.extend(block_data['rows'])
                    self.login_progress.emit(f"✅ 时间块 {block_index} 完成，获取 {block_records} 条记录")
                else:
                    self.login_progress.emit(f"⚠️ 时间块 {block_index} 无数据")

                # 时间块间隔（避免请求过于频繁）
                if block_index < len(date_blocks):
                    self.login_progress.emit("⏳ 等待2秒后处理下一个时间块...")
                    time.sleep(2)

            # 构建最终结果
            if all_final_data:
                final_result = {
                    "total": str(total_records_all_blocks),
                    "rows": all_final_data
                }

                self.login_progress.emit(f"🎉 分块查询完成！共处理 {len(date_blocks)} 个时间块，总计获取 {total_records_all_blocks} 条记录")
                return final_result
            else:
                self.login_progress.emit("❌ 所有时间块查询均无数据返回")
                return None

        except Exception as e:
            self.login_progress.emit(f"❌ 分块查询执行失败: {str(e)}")
            return None

    def execute_dual_query(self, start_date, end_date, headers, cookies_dict):
        """
        执行双重查询（table_flag为是和否）并合并结果

        :param start_date: 开始日期
        :param end_date: 结束日期
        :param headers: 请求头
        :param cookies_dict: cookies字典
        :return: 合并后的完整数据
        """
        try:
            # 查询配置
            query_configs = [
                {
                    "name": "表格显示-是",
                    "table_flag": TableFlag.YES.value,
                    "description": "获取表格显示为'是'的数据"
                },
                {
                    "name": "表格显示-否",
                    "table_flag": TableFlag.NO.value,
                    "description": "获取表格显示为'否'的数据"
                }
            ]

            all_merged_data = []
            total_records_sum = 0

            # 执行两个查询
            for i, config in enumerate(query_configs, 1):
                self.login_progress.emit(f"🔍 开始第{i}个查询：{config['name']} ({config['description']})")

                # 创建查询参数
                encoded_params = self.create_custom_query(
                    start_date=start_date,
                    end_date=end_date,
                    table_flag=config['table_flag'],
                    ie_flag=IEFlag.EXPORT.value,
                    etps_category=EtpsCategory.COMPANY.value
                )

                # 执行分页查询
                query_data = self.execute_paginated_query(
                    encoded_params, headers, cookies_dict, config['name']
                )

                if query_data:
                    query_records = len(query_data)
                    total_records_sum += query_records
                    all_merged_data.extend(query_data)
                    self.login_progress.emit(f"✅ {config['name']} 查询完成，获取 {query_records} 条记录")
                else:
                    self.login_progress.emit(f"⚠️ {config['name']} 查询无数据")

                # 查询间隔
                if i < len(query_configs):
                    self.login_progress.emit("⏳ 等待1秒后进行下一个查询...")
                    time.sleep(1)

            # 构建最终结果
            if all_merged_data:
                final_result = {
                    "total": str(total_records_sum),
                    "rows": all_merged_data
                }

                self.login_progress.emit(f"🎉 双重查询完成！总计获取 {total_records_sum} 条记录")
                return final_result
            else:
                self.login_progress.emit("❌ 双重查询均无数据返回")
                return None

        except Exception as e:
            self.login_progress.emit(f"❌ 双重查询执行失败: {str(e)}")
            return None

    def execute_paginated_query(self, encoded_params, headers, cookies_dict, query_name):
        """
        执行单个查询的分页获取

        :param encoded_params: 编码后的查询参数
        :param headers: 请求头
        :param cookies_dict: cookies字典
        :param query_name: 查询名称（用于日志）
        :return: 该查询的所有数据列表
        """
        try:
            all_data = []
            page_size = 50  # 每页获取50条数据
            offset = 0
            total_records = None

            while True:
                # 设置请求参数
                params = {
                    'limit': str(page_size),
                    'offset': str(offset),
                    'stName': 'updateTime',
                    'stOrder': 'desc',
                    'decStatusInfo': encoded_params,
                    '_': str(int(datetime.now().timestamp() * 1000)),  # 当前时间戳
                }

                # 发送请求
                page_num = offset // page_size + 1
                self.login_progress.emit(f"  📄 {query_name} - 第{page_num}页（偏移量: {offset}）")

                response = requests.get(
                    'https://sz.singlewindow.cn/dyck/swProxy/decserver/sw/dec/merge/cusQueryNew',
                    params=params,
                    headers=headers,
                    cookies=cookies_dict,
                    timeout=30
                )

                if response.status_code != 200:
                    self.login_progress.emit(f"  ❌ {query_name} - 第{page_num}页获取失败，状态码: {response.status_code}")
                    break

                try:
                    page_data = json.loads(response.text)

                    # 获取总记录数（第一次请求时）
                    if total_records is None:
                        total_records = int(page_data.get('total', 0))
                        if total_records > 0:
                            self.login_progress.emit(f"  📊 {query_name} - 发现 {total_records} 条记录")
                        else:
                            self.login_progress.emit(f"  📊 {query_name} - 无数据")
                            break

                    # 获取当前页的数据
                    current_rows = page_data.get('rows', [])
                    if not current_rows:
                        self.login_progress.emit(f"  📄 {query_name} - 当前页无数据，停止获取")
                        break

                    # 添加到总数据中
                    all_data.extend(current_rows)

                    current_count = len(all_data)
                    self.login_progress.emit(f"  ✅ {query_name} - 第{page_num}页成功，已获取 {current_count}/{total_records} 条")

                    # 检查是否已获取所有数据
                    if current_count >= total_records or len(current_rows) < page_size:
                        break

                    # 准备下一页
                    offset += page_size

                    # 添加短暂延迟，避免请求过于频繁
                    time.sleep(0.3)

                except json.JSONDecodeError as e:
                    self.login_progress.emit(f"  ❌ {query_name} - 第{page_num}页数据解析失败: {str(e)}")
                    break
                except Exception as e:
                    self.login_progress.emit(f"  ❌ {query_name} - 第{page_num}页处理失败: {str(e)}")
                    break

            return all_data

        except Exception as e:
            self.login_progress.emit(f"❌ {query_name} - 分页查询执行失败: {str(e)}")
            return []

    def cleanup_browser_session(self, page):
        """清理浏览器session和临时目录"""
        try:
            self.login_progress.emit("正在清理浏览器session...")

            if page:
                # 获取用户数据目录路径（如果可能的话）
                temp_dir = None
                try:
                    # 尝试获取用户数据目录
                    if hasattr(page, 'driver') and hasattr(page.driver, 'options'):
                        for arg in page.driver.options.arguments:
                            if arg.startswith('--user-data-dir='):
                                temp_dir = arg.replace('--user-data-dir=', '')
                                break
                except:
                    pass

                # 关闭浏览器
                try:
                    page.quit()
                    self.login_progress.emit("✅ 浏览器已关闭")
                except Exception as e:
                    self.login_progress.emit(f"⚠️ 关闭浏览器时出错: {str(e)}")

                # 清理临时目录
                if temp_dir:
                    try:
                        import shutil
                        import os
                        if os.path.exists(temp_dir):
                            shutil.rmtree(temp_dir, ignore_errors=True)
                            self.login_progress.emit(f"✅ 已清理临时目录: {temp_dir}")
                    except Exception as e:
                        self.login_progress.emit(f"⚠️ 清理临时目录时出错: {str(e)}")

            self.login_progress.emit("🔒 账号session已完全清理，确保账号间隔离")

        except Exception as e:
            self.login_progress.emit(f"⚠️ 清理过程中出错: {str(e)}")


class LoginWindow(QMainWindow):
    """登录窗口主类"""
    
    def __init__(self):
        super().__init__()
        self.account_manager = AccountManager()
        self.login_thread = None
        self.init_ui()
        self.load_accounts_to_combo()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("报关单下载程序")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧：账号管理区域
        left_widget = self.create_account_management_area()
        splitter.addWidget(left_widget)
        
        # 右侧：日志区域
        right_widget = self.create_log_area()
        splitter.addWidget(right_widget)
        
        # 设置分割器比例
        splitter.setSizes([400, 400])
        
        # 设置样式
        self.setStyleSheet(self.get_stylesheet())
    
    def create_account_management_area(self):
        """创建账号管理区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 账号选择组
        account_group = QGroupBox("账号选择")
        account_layout = QFormLayout(account_group)
        
        self.account_combo = QComboBox()
        self.account_combo.currentTextChanged.connect(self.on_account_selected)
        account_layout.addRow("选择账号:", self.account_combo)
        
        layout.addWidget(account_group)
        
        # 账号信息组
        info_group = QGroupBox("账号信息")
        info_layout = QFormLayout(info_group)

        self.account_name_edit = QLineEdit()
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)

        # 日期选择器
        self.start_date_edit = QDateEdit()
        self.end_date_edit = QDateEdit()

        # 设置默认日期为前一个星期五及其前六天
        start_date_str, end_date_str = getsetime()
        default_start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        default_end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")

        self.start_date_edit.setDate(default_start_date)
        self.start_date_edit.setCalendarPopup(True)

        self.end_date_edit.setDate(default_end_date)
        self.end_date_edit.setCalendarPopup(True)

        # 分块天数设置
        self.block_days_spinbox = QSpinBox()
        self.block_days_spinbox.setMinimum(1)
        self.block_days_spinbox.setMaximum(30)
        self.block_days_spinbox.setValue(7)  # 默认7天
        self.block_days_spinbox.setSuffix(" 天")
        self.block_days_spinbox.setToolTip("当日期范围较大时，将按此天数分块查询，避免单次查询数据过多")

        # 文件保存路径设置
        self.save_path_edit = QLineEdit()
        self.save_path_edit.setPlaceholderText("选择文件保存路径...")
        self.save_path_edit.setText(get_default_save_path())  # 默认为桌面路径
        self.save_path_edit.setReadOnly(True)

        self.browse_path_button = QPushButton("浏览...")
        self.browse_path_button.clicked.connect(self.browse_save_path)
        self.browse_path_button.setMaximumWidth(80)

        info_layout.addRow("账号名称:", self.account_name_edit)
        info_layout.addRow("密码:", self.password_edit)
        info_layout.addRow("开始日期:", self.start_date_edit)
        info_layout.addRow("结束日期:", self.end_date_edit)
        info_layout.addRow("分块天数:", self.block_days_spinbox)

        # 文件保存路径行
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.save_path_edit)
        path_layout.addWidget(self.browse_path_button)
        path_widget = QWidget()
        path_widget.setLayout(path_layout)
        info_layout.addRow("保存路径:", path_widget)
        
        layout.addWidget(info_group)
        
        # 按钮组
        button_layout = QHBoxLayout()
        
        self.save_button = QPushButton("保存账号")
        self.save_button.clicked.connect(self.save_account)
        
        self.delete_button = QPushButton("删除账号")
        self.delete_button.clicked.connect(self.delete_account)
        
        self.clear_button = QPushButton("清空表单")
        self.clear_button.clicked.connect(self.clear_form)

        self.api_key_button = QPushButton("API密钥管理")
        self.api_key_button.clicked.connect(self.manage_api_keys)
        self.api_key_button.setStyleSheet("QPushButton { background-color: #2196F3; color: white; font-weight: bold; }")

        self.login_button = QPushButton("登录")
        self.login_button.clicked.connect(self.login_account)
        self.login_button.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }")

        button_layout.addWidget(self.save_button)
        button_layout.addWidget(self.delete_button)
        button_layout.addWidget(self.clear_button)
        button_layout.addWidget(self.api_key_button)
        button_layout.addWidget(self.login_button)
        
        layout.addLayout(button_layout)
        
        # 添加弹性空间
        layout.addStretch()
        
        return widget
    
    def create_log_area(self):
        """创建日志区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 日志组
        log_group = QGroupBox("操作日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        # 设置最大文档大小来限制内容
        self.log_text.document().setMaximumBlockCount(1000)
        log_layout.addWidget(self.log_text)
        
        # 清空日志按钮
        clear_log_button = QPushButton("清空日志")
        clear_log_button.clicked.connect(self.clear_log)
        log_layout.addWidget(clear_log_button)
        
        layout.addWidget(log_group)
        
        return widget
    
    def load_accounts_to_combo(self):
        """加载账号到下拉框"""
        self.account_combo.clear()
        self.account_combo.addItem("-- 选择账号 --")

        account_names = self.account_manager.get_account_names()
        for name in account_names:
            self.account_combo.addItem(name)

        # 默认选择"西格玛"账号
        if "西格玛" in account_names:
            index = self.account_combo.findText("西格玛")
            if index >= 0:
                self.account_combo.setCurrentIndex(index)
                self.add_log(f"已加载 {len(account_names)} 个账号，默认选择：西格玛")
            else:
                self.add_log(f"已加载 {len(account_names)} 个账号")
        else:
            self.add_log(f"已加载 {len(account_names)} 个账号，未找到西格玛账号")
    
    def on_account_selected(self, account_name):
        """账号选择事件"""
        if account_name == "-- 选择账号 --" or not account_name:
            self.clear_form()
            return

        account = self.account_manager.get_account(account_name)
        if account:
            self.account_name_edit.setText(account_name)
            self.password_edit.setText(account.get('password', ''))

            # 始终使用默认日期，不管账号中是否保存了日期
            default_start_str, default_end_str = getsetime()
            default_start_date = QDate.fromString(default_start_str, "yyyy-MM-dd")
            default_end_date = QDate.fromString(default_end_str, "yyyy-MM-dd")

            # 设置为默认日期
            self.start_date_edit.setDate(default_start_date)
            self.end_date_edit.setDate(default_end_date)

            self.add_log(f"已选择账号: {account_name}")
    
    def save_account(self):
        """保存账号"""
        account_name = self.account_name_edit.text().strip()
        password = self.password_edit.text().strip()

        # 获取当前界面上的日期
        current_start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        current_end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

        # 获取默认日期范围
        default_start_date, default_end_date = getsetime()

        # 如果当前日期是默认日期，或者是新账号，则使用默认日期
        # 这样确保所有账号都有一致的默认日期范围
        start_date = default_start_date
        end_date = default_end_date

        if not account_name:
            QMessageBox.warning(self, "警告", "请输入账号名称！")
            return

        if not password:
            QMessageBox.warning(self, "警告", "请输入密码！")
            return

        # 检查是否是更新现有账号
        if self.account_manager.account_exists(account_name):
            reply = QMessageBox.question(self, "确认", f"账号 '{account_name}' 已存在，是否更新？\n将使用默认日期范围: {start_date} 到 {end_date}",
                                       QMessageBox.Yes | QMessageBox.No)
            if reply == QMessageBox.Yes:
                success = self.account_manager.update_account_with_dates(
                    account_name, password, start_date, end_date)
                if success:
                    self.add_log(f"✅ 成功更新账号: {account_name} (日期: {start_date} 到 {end_date})")
                    QMessageBox.information(self, "成功", "账号更新成功！")
                else:
                    self.add_log(f"❌ 更新账号失败: {account_name}")
                    QMessageBox.critical(self, "错误", "账号更新失败！")
        else:
            success = self.account_manager.add_account_with_dates(
                account_name, password, start_date, end_date)
            if success:
                self.add_log(f"✅ 成功添加账号: {account_name} (日期: {start_date} 到 {end_date})")
                QMessageBox.information(self, "成功", "账号添加成功！")
                self.load_accounts_to_combo()  # 重新加载下拉框
                # 选择新添加的账号
                index = self.account_combo.findText(account_name)
                if index >= 0:
                    self.account_combo.setCurrentIndex(index)
            else:
                self.add_log(f"❌ 添加账号失败: {account_name}")
                QMessageBox.critical(self, "错误", "账号添加失败！")
    
    def delete_account(self):
        """删除账号"""
        account_name = self.account_name_edit.text().strip()
        
        if not account_name:
            QMessageBox.warning(self, "警告", "请先选择要删除的账号！")
            return
        
        if not self.account_manager.account_exists(account_name):
            QMessageBox.warning(self, "警告", f"账号 '{account_name}' 不存在！")
            return
        
        reply = QMessageBox.question(self, "确认删除", 
                                   f"确定要删除账号 '{account_name}' 吗？\n此操作不可撤销！",
                                   QMessageBox.Yes | QMessageBox.No)
        
        if reply == QMessageBox.Yes:
            success = self.account_manager.delete_account(account_name)
            if success:
                self.add_log(f"✅ 成功删除账号: {account_name}")
                QMessageBox.information(self, "成功", "账号删除成功！")
                self.load_accounts_to_combo()  # 重新加载下拉框
                self.clear_form()  # 清空表单
            else:
                self.add_log(f"❌ 删除账号失败: {account_name}")
                QMessageBox.critical(self, "错误", "账号删除失败！")
    
    def browse_save_path(self):
        """浏览选择保存路径"""
        try:
            current_path = self.save_path_edit.text()
            if not current_path or not os.path.exists(current_path):
                current_path = get_default_save_path()

            selected_path = QFileDialog.getExistingDirectory(
                self,
                "选择文件保存路径",
                current_path,
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )

            if selected_path:
                self.save_path_edit.setText(selected_path)
                self.add_log(f"📁 保存路径已设置为: {selected_path}")

        except Exception as e:
            self.add_log(f"❌ 选择保存路径失败: {str(e)}")
            QMessageBox.warning(self, "警告", f"选择保存路径失败: {str(e)}")

    def get_save_path(self):
        """获取当前设置的保存路径"""
        save_path = self.save_path_edit.text().strip()
        if not save_path or not os.path.exists(save_path):
            save_path = get_default_save_path()
            self.save_path_edit.setText(save_path)
        return save_path

    def clear_form(self):
        """清空表单"""
        self.account_name_edit.clear()
        self.password_edit.clear()

        # 设置默认日期为前一个星期五及其前六天
        start_date_str, end_date_str = getsetime()
        default_start_date = QDate.fromString(start_date_str, "yyyy-MM-dd")
        default_end_date = QDate.fromString(end_date_str, "yyyy-MM-dd")

        self.start_date_edit.setDate(default_start_date)
        self.end_date_edit.setDate(default_end_date)
        self.account_combo.setCurrentIndex(0)
    
    def login_account(self):
        """登录账号"""
        account_name = self.account_name_edit.text().strip()
        
        if not account_name:
            QMessageBox.warning(self, "警告", "请先选择要登录的账号！")
            return
        
        account = self.account_manager.get_account(account_name)
        if not account:
            QMessageBox.warning(self, "警告", f"账号 '{account_name}' 不存在！")
            return
        
        # 禁用登录按钮
        self.login_button.setEnabled(False)
        self.login_button.setText("登录中...")
        
        # 获取日期信息
        start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
        end_date = self.end_date_edit.date().toString("yyyy-MM-dd")
        block_days = self.block_days_spinbox.value()

        # 创建并启动登录线程
        self.login_thread = LoginThread(account, start_date, end_date, account_name, self.account_manager, block_days)
        self.login_thread.login_success.connect(self.on_login_success)
        self.login_thread.login_failed.connect(self.on_login_failed)
        self.login_thread.login_progress.connect(self.add_log)
        self.login_thread.data_received.connect(self.on_data_received)
        self.login_thread.finished.connect(self.on_login_finished)
        self.login_thread.start()
        
        self.add_log(f"🚀 开始登录账号: {account_name}")
    
    def on_login_success(self, message):
        """登录成功处理"""
        self.add_log(message)
        # 注释掉弹窗提示，只在日志中显示
        # QMessageBox.information(self, "登录成功", message)

        # 更新最后使用时间
        account_name = self.account_name_edit.text().strip()
        self.account_manager.update_last_used(account_name)
    
    def on_login_failed(self, message):
        """登录失败处理"""
        self.add_log(message)

        # 根据失败原因提供更详细的用户提示
        detailed_message = message
        if "登录状态异常" in message:
            detailed_message += "\n\n可能的解决方案：\n1. 检查网络连接是否稳定\n2. 确认密码是否正确\n3. 稍后重试（网站可能繁忙）\n4. 清除浏览器缓存后重试"
        elif "网络" in message or "连接" in message:
            detailed_message += "\n\n可能的解决方案：\n1. 检查网络连接\n2. 尝试更换网络环境\n3. 稍后重试"
        elif "密码" in message:
            detailed_message += "\n\n可能的解决方案：\n1. 确认密码是否正确\n2. 检查是否有大小写错误\n3. 联系管理员确认账号状态"
        else:
            detailed_message += "\n\n建议：稍后重试，或联系技术支持"

        QMessageBox.critical(self, "登录失败", detailed_message)

    def on_data_received(self, data):
        """数据接收处理"""
        try:
            # 解析JSON数据
            json_data = json.loads(data)

            # 格式化显示数据
            formatted_data = json.dumps(json_data, ensure_ascii=False, indent=2)

            # 在日志中显示数据摘要
            rows = json_data.get('rows', [])
            total = json_data.get('total', '0')

            if rows:
                rows_count = len(rows)
                self.add_log(f"📊 获取到 {rows_count} 条记录，总计 {total} 条")

                # 显示前几条记录的摘要
                for i, row in enumerate(rows[:3]):  # 只显示前3条
                    entry_id = row.get('entryId', 'N/A')
                    bill_no = row.get('billNo', 'N/A')
                    consignor = row.get('consignorCname', 'N/A')
                    self.add_log(f"  {i+1}. 海关编号: {entry_id}, 提单号: {bill_no}, 发货人: {consignor}")

                if rows_count > 3:
                    self.add_log(f"  ... 还有 {rows_count - 3} 条记录")

                # 导出到Excel
                self.add_log("📋 正在导出数据到Excel...")
                try:
                    # 获取保存路径和账号信息
                    save_path = self.get_save_path()
                    account_name = self.account_name_edit.text().strip()
                    start_date = self.start_date_edit.date().toString("yyyy-MM-dd")
                    end_date = self.end_date_edit.date().toString("yyyy-MM-dd")

                    # 导出Excel到账号目录
                    excel_filename = export_to_excel(
                        json_data,
                        save_path=save_path,
                        account_name=account_name,
                        start_date=start_date,
                        end_date=end_date
                    )
                    self.add_log(f"✅ Excel导出成功: {excel_filename}")

                    # 显示导出的字段信息
                    self.add_log(f"📄 导出字段数量: {len(FIELD_MAPPING)} 个")
                    self.add_log("📋 导出字段列表:")
                    for chinese_name, field_name in list(FIELD_MAPPING.items())[:5]:  # 显示前5个字段
                        self.add_log(f"  • {chinese_name} ({field_name})")
                    if len(FIELD_MAPPING) > 5:
                        self.add_log(f"  ... 还有 {len(FIELD_MAPPING) - 5} 个字段")

                except Exception as excel_error:
                    self.add_log(f"❌ Excel导出失败: {str(excel_error)}")



            else:
                self.add_log("📊 未获取到数据记录")



        except json.JSONDecodeError as e:
            self.add_log(f"❌ 数据解析失败: {str(e)}")
            self.add_log(f"原始数据: {data[:200]}...")  # 只显示前200个字符
        except Exception as e:
            self.add_log(f"❌ 数据处理失败: {str(e)}")



    def on_login_finished(self):
        """登录完成处理"""
        self.login_button.setEnabled(True)
        self.login_button.setText("登录")
    
    def add_log(self, message):
        """添加日志"""
        from datetime import datetime
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_message = f"[{timestamp}] {message}"
        self.log_text.append(log_message)
        
        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """清空日志"""
        self.log_text.clear()
        self.add_log("日志已清空")

    def manage_api_keys(self):
        """API密钥管理对话框"""
        from PyQt5.QtWidgets import QDialog, QVBoxLayout, QHBoxLayout, QLabel, QLineEdit, QPushButton, QListWidget, QMessageBox, QTextEdit

        dialog = QDialog(self)
        dialog.setWindowTitle("API密钥管理")
        dialog.setModal(True)
        dialog.resize(600, 500)

        layout = QVBoxLayout(dialog)

        # 当前API密钥状态
        status_group = QGroupBox("当前状态")
        status_layout = QVBoxLayout(status_group)

        current_api = self.account_manager.get_current_api_key()
        if current_api:
            status_text = f"当前使用: {current_api.get('app_id')} ({current_api.get('description', '无描述')})\n"
            status_text += f"状态: {current_api.get('status', 'unknown')}\n"
            status_text += f"使用次数: {current_api.get('usage_count', 0)}"
        else:
            status_text = "没有可用的API密钥"

        status_label = QLabel(status_text)
        status_layout.addWidget(status_label)
        layout.addWidget(status_group)

        # 添加新API密钥
        add_group = QGroupBox("添加新API密钥")
        add_layout = QFormLayout(add_group)

        app_id_edit = QLineEdit()
        app_id_edit.setPlaceholderText("输入x-ti-app-id")

        secret_edit = QLineEdit()
        secret_edit.setPlaceholderText("输入x-ti-secret-code")
        secret_edit.setEchoMode(QLineEdit.Password)

        desc_edit = QLineEdit()
        desc_edit.setPlaceholderText("描述信息（可选）")

        add_layout.addRow("App ID:", app_id_edit)
        add_layout.addRow("Secret Code:", secret_edit)
        add_layout.addRow("描述:", desc_edit)

        add_button = QPushButton("添加API密钥")
        add_layout.addRow(add_button)

        layout.addWidget(add_group)

        # API密钥列表
        list_group = QGroupBox("已有API密钥")
        list_layout = QVBoxLayout(list_group)

        api_list = QListWidget()
        self.refresh_api_list(api_list)
        list_layout.addWidget(api_list)

        # 列表操作按钮
        list_button_layout = QHBoxLayout()
        refresh_button = QPushButton("刷新")
        delete_button = QPushButton("删除选中")
        test_button = QPushButton("测试选中")

        list_button_layout.addWidget(refresh_button)
        list_button_layout.addWidget(delete_button)
        list_button_layout.addWidget(test_button)
        list_layout.addLayout(list_button_layout)

        layout.addWidget(list_group)

        # 关闭按钮
        close_button = QPushButton("关闭")
        layout.addWidget(close_button)

        # 事件绑定
        def add_api_key():
            app_id = app_id_edit.text().strip()
            secret_code = secret_edit.text().strip()
            description = desc_edit.text().strip()

            if not app_id or not secret_code:
                QMessageBox.warning(dialog, "警告", "App ID和Secret Code不能为空！")
                return

            if self.account_manager.add_api_key(app_id, secret_code, description):
                QMessageBox.information(dialog, "成功", "API密钥添加成功！")
                app_id_edit.clear()
                secret_edit.clear()
                desc_edit.clear()
                self.refresh_api_list(api_list)
                # 更新状态显示
                current_api = self.account_manager.get_current_api_key()
                if current_api:
                    status_text = f"当前使用: {current_api.get('app_id')} ({current_api.get('description', '无描述')})\n"
                    status_text += f"状态: {current_api.get('status', 'unknown')}\n"
                    status_text += f"使用次数: {current_api.get('usage_count', 0)}"
                    status_label.setText(status_text)
            else:
                QMessageBox.critical(dialog, "错误", "API密钥添加失败！")

        def refresh_list():
            self.refresh_api_list(api_list)

        def delete_selected():
            current_item = api_list.currentItem()
            if not current_item:
                QMessageBox.warning(dialog, "警告", "请先选择要删除的API密钥！")
                return

            app_id = current_item.text().split(' - ')[0]
            reply = QMessageBox.question(dialog, "确认", f"确定要删除API密钥 {app_id} 吗？")
            if reply == QMessageBox.Yes:
                if self.account_manager.remove_api_key(app_id):
                    QMessageBox.information(dialog, "成功", "API密钥删除成功！")
                    self.refresh_api_list(api_list)
                else:
                    QMessageBox.critical(dialog, "错误", "API密钥删除失败！")

        def test_selected():
            current_item = api_list.currentItem()
            if not current_item:
                QMessageBox.warning(dialog, "警告", "请先选择要测试的API密钥！")
                return

            QMessageBox.information(dialog, "提示", "API密钥测试功能暂未实现，将在实际使用时自动验证。")

        add_button.clicked.connect(add_api_key)
        refresh_button.clicked.connect(refresh_list)
        delete_button.clicked.connect(delete_selected)
        test_button.clicked.connect(test_selected)
        close_button.clicked.connect(dialog.close)

        dialog.exec_()

    def refresh_api_list(self, api_list):
        """刷新API密钥列表"""
        api_list.clear()
        api_keys = self.account_manager.get_all_api_keys()

        for i, api_key in enumerate(api_keys):
            app_id = api_key.get('app_id', '')
            description = api_key.get('description', '无描述')
            status = api_key.get('status', 'unknown')
            usage_count = api_key.get('usage_count', 0)

            # 标记当前使用的API密钥
            current_marker = " [当前]" if i == self.account_manager.current_api_index else ""

            item_text = f"{app_id} - {description} ({status}, 使用{usage_count}次){current_marker}"
            api_list.addItem(item_text)
    
    def get_stylesheet(self):
        """获取样式表"""
        return """
        QMainWindow {
            background-color: #f5f5f5;
        }
        QGroupBox {
            font-weight: bold;
            border: 2px solid #cccccc;
            border-radius: 5px;
            margin-top: 1ex;
            padding-top: 10px;
        }
        QGroupBox::title {
            subcontrol-origin: margin;
            left: 10px;
            padding: 0 5px 0 5px;
        }
        QPushButton {
            background-color: #0084ff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            font-weight: bold;
        }
        QPushButton:hover {
            background-color: #0066cc;
        }
        QPushButton:pressed {
            background-color: #004499;
        }
        QPushButton:disabled {
            background-color: #cccccc;
            color: #666666;
        }
        QLineEdit, QComboBox {
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: white;
        }
        QTextEdit {
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: "Consolas", "Monaco", monospace;
        }
        """


def main():
    """主函数"""
    app = QApplication(sys.argv)
    app.setApplicationName("账号管理与登录系统")
    
    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon('icon.png'))
    
    window = LoginWindow()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
